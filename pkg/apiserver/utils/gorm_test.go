// Copyright 2025 PingCAP, Inc. Licensed under Apache-2.0.

package utils

import (
	"testing"

	"github.com/pingcap/check"
)

func TestT(t *testing.T) {
	check.CustomVerboseFlag = true
	check.TestingT(t)
}

var _ = check.Suite(&testGormSuite{})

type testGormSuite struct{}

func (t *testGormSuite) Test_GetGormColumnName(c *check.C) {
	c.<PERSON>(GetGormColumnName(`column:db`), check.Equals, `db`)
	c.<PERSON>ser<PERSON>(GetGormColumnName(`primaryKey;index`), check.Equals, ``)
	c.<PERSON>sert(GetGormColumnName(`column:db;primaryKey;index`), check.Equals, `db`)
}
