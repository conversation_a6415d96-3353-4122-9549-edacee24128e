global:
  user: tidb
  deploy_dir: tidb-deploy
  data_dir: tidb-data

server_configs:
  tikv:
    server.grpc-concurrency: 1
    raftstore.apply-pool-size: 1
    raftstore.store-pool-size: 1
    readpool.unified.max-thread-count: 1
    readpool.storage.use-unified-pool: false
    readpool.coprocessor.use-unified-pool: true
    storage.block-cache.capacity: 256MB
    raftstore.capacity: 10GB
  pd:
    replication.enable-placement-rules: true

pd_servers:
  - host: *********
  - host: *********
  - host: *********

tikv_servers:
  - host: *********

tidb_servers:
  - host: *********
  - host: *********
  - host: *********

tiflash_servers:
  - host: *********

grafana_servers:
  - host: *********

monitoring_servers:
  - host: *********

alertmanager_servers:
  - host: *********
