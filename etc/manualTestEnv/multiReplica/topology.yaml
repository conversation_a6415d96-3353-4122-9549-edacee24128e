global:
  user: tidb
  deploy_dir: tidb-deploy
  data_dir: tidb-data

server_configs:
  tikv:
    server.grpc-concurrency: 1
    raftstore.apply-pool-size: 1
    raftstore.store-pool-size: 1
    readpool.unified.max-thread-count: 1
    readpool.storage.use-unified-pool: false
    readpool.coprocessor.use-unified-pool: true
    storage.block-cache.capacity: 256MB
    raftstore.capacity: 10GB
  pd:
    replication.location-labels:
      - zone
      - rack
      - host

pd_servers:
  - host: *********

tikv_servers:
  - host: *********
    port: 20160
    status_port: 20180
    config:
      server.labels: { host: tikv1, rack: rack1 }
  - host: *********
    port: 20161
    status_port: 20181
    config:
      server.labels: { host: tikv1, rack: rack1 }
  - host: *********
    port: 20162
    status_port: 20182
    config:
      server.labels: { host: tikv2, rack: rack1 }
  - host: *********
    port: 20163
    status_port: 20183
    config:
      server.labels: { host: tikv2, rack: rack1 }
  - host: *********
    port: 20164
    status_port: 20184
    config:
      server.labels: { host: tikv3, rack: rack2 }
  - host: *********
    port: 20165
    status_port: 20185
    config:
      server.labels: { host: tikv3, rack: rack2 }

tidb_servers:
  - host: *********

grafana_servers:
  - host: *********

monitoring_servers:
  - host: *********
