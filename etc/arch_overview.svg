<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="691px" height="539px" viewBox="-0.5 -0.5 691 539" content="&lt;mxfile host=&quot;www.draw.io&quot; modified=&quot;2020-01-15T17:23:44.105Z&quot; agent=&quot;Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/79.0.3945.117 Safari/537.36&quot; etag=&quot;nDe-EHjnd0LNLBZ5CX_7&quot; version=&quot;12.5.4&quot; type=&quot;device&quot;&gt;&lt;diagram id=&quot;7Ai1qhs1gsBMxDJCnMHj&quot; name=&quot;Page-1&quot;&gt;3Vhbe6IwEP01PrYft6A+Vu1l736t224fIwmQLhI2xir763eQQUCsbVddu/VBmZML5Jw5GUnL7k8Wl4om4RfJeNSyDLZo2YOWZZkd24WfDElzpG05ORAowbBTCdyI3xxBA9GZYHxa66iljLRI6qAn45h7uoZRpeS83s2XUf2uCQ14A7jxaNRE7wTTYY66xCjxKy6CEO9MDGyY0KIvAtOQMjmvQPZ5y+4rKXV+NVn0eZRxV9CSj7t4onX1XIrH+iUDrpJfqf/tLiDXH5NB7+stfxCXJ4UYjzSa4YLxaXVaMADTANkQ9Oah0PwmoV7WMge9AQv1JILIhEtGpyFnGPgiivoykgriWMbZcF/GGgU2HYiV1FQLGUN84hoANJeEq3zkSvNFBcIlXnI54Vql0AVb28g2ZhvpnhLHqHwwG+ellLZNciysyOgU81BMn2B1p5JiuECWX8G42XmecSVnMVsyaTzP+guIrgkz1Ur+5MWIlmVfXBhGZ0/0d+v0W4UbKnyTwjpVvlcu2T/fpJnQDByOoVQ6lIGMaXReor26AGWfz1ImSOMD1zpFkulMy7oowKBKf+D4ZXCfBaekCAeLauMgxWhduSclmcqZ8vi2deNeSVXA9bYdAInPSNmqsOIRuPWxvi1ukguHDqWAZ15lhmOQemo4pD5F/qQ4ak301WPskAfuUfJgIfQyDU7bBMP7Qni4LrMgC9JqSvwXyWPa+06e3bzePq7G1rvU2HlbGjfK5wCq21hSxQC+Go2G8HM2/LBjUV2juVpkoWQyyju+t6mYul6Hj/39FFPTXKumVrOarrBqNXUPVkzNZ2jdyT5/457SMPfV0ntw91gvdc+bMo+11TzfD2sa3/ctb6NpmDt2iXsY09j20U1jN1gfiUEve7e9Hvb/xW5FeIc5m4jvWGPb3RPx63/wzOPvVs2X25H4dPveiT8+790G70P4MsYipjh9lXFYpq7TSiMRZKcCHpDCgbFeRobwaHSGDRPBWP7njU/FbzpeTpXplWSvD8v1kF6LDLK5oOBMUaJNikF8oHdh4q7tRN2mMJ1N78KH0qWYeF2X9+2Go9vBIlur7jVPIEG1fOPOyI/P9qORZded4ThNjcyNx0Sv1wjC8ow1P0woD6rt8z8=&lt;/diagram&gt;&lt;/mxfile&gt;"><defs/><g><ellipse cx="287.5" cy="274.54" rx="167.5" ry="235" fill="none" stroke="#000000" stroke-dasharray="3 3" transform="rotate(-60,287.5,274.54)" pointer-events="all"/><rect x="140" y="180" width="550" height="220" fill="none" stroke="#ff0080" stroke-dasharray="3 3" pointer-events="all"/><path d="M 380 230 L 448.63 230" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 453.88 230 L 446.88 233.5 L 448.63 230 L 446.88 226.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 325 200 L 325 130 L 448.63 130" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 453.88 130 L 446.88 133.5 L 448.63 130 L 446.88 126.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 215 200 L 215 30 L 448.63 30" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 453.88 30 L 446.88 33.5 L 448.63 30 L 446.88 26.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><rect x="160" y="200" width="220" height="60" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 216px; height: 1px; padding-top: 230px; margin-left: 162px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 14px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Dashboard HTTP API</div></div></div></foreignObject><text x="270" y="234" fill="#000000" font-family="Helvetica" font-size="14px" text-anchor="middle">Dashboard HTTP API</text></switch></g><path d="M 270 310 L 270 266.37" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 270 261.12 L 273.5 268.12 L 270 266.37 L 266.5 268.12 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><rect x="160" y="310" width="220" height="60" fill="#fff2cc" stroke="#d6b656" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 216px; height: 1px; padding-top: 340px; margin-left: 162px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 14px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Dashboard UI</div></div></div></foreignObject><text x="270" y="344" fill="#000000" font-family="Helvetica" font-size="14px" text-anchor="middle">Dashboard UI</text></switch></g><rect x="455" y="100" width="220" height="60" fill="#d5e8d4" stroke="#82b366" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 216px; height: 1px; padding-top: 130px; margin-left: 457px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 14px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">TiDB gRPC API</div></div></div></foreignObject><text x="565" y="134" fill="#000000" font-family="Helvetica" font-size="14px" text-anchor="middle">TiDB gRPC API</text></switch></g><rect x="455" y="0" width="220" height="60" fill="#d5e8d4" stroke="#82b366" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 216px; height: 1px; padding-top: 30px; margin-left: 457px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 14px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">TiKV gRPC API</div></div></div></foreignObject><text x="565" y="34" fill="#000000" font-family="Helvetica" font-size="14px" text-anchor="middle">TiKV gRPC API</text></switch></g><rect x="610" y="370" width="80" height="20" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 380px; margin-left: 650px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 14px; font-family: Helvetica; color: #FF0080; line-height: 1.2; pointer-events: all; white-space: nowrap; ">PD binary</div></div></div></foreignObject><text x="650" y="384" fill="#FF0080" font-family="Helvetica" font-size="14px" text-anchor="middle">PD binary</text></switch></g><rect x="455" y="200" width="220" height="60" fill="#d5e8d4" stroke="#82b366" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 216px; height: 1px; padding-top: 230px; margin-left: 457px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 14px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">PD gRPC API</div></div></div></foreignObject><text x="565" y="234" fill="#000000" font-family="Helvetica" font-size="14px" text-anchor="middle">PD gRPC API</text></switch></g><rect x="280" y="420" width="150" height="20" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 430px; margin-left: 355px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 14px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; ">Dashboard Repository</div></div></div></foreignObject><text x="355" y="434" fill="#000000" font-family="Helvetica" font-size="14px" text-anchor="middle">Dashboard Repository</text></switch></g></g></svg>