name: Test Docker Image
on:
  push:
    branches:
      - master
  pull_request:
    branches:
      - master
concurrency:
  group: ${{ github.ref }}-${{ github.workflow }}
  cancel-in-progress: true
jobs:
  test-build-docker-image:
    runs-on: ubuntu-latest
    strategy:
      fail-fast: true
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      - # Add support for more platforms with QEMU (optional)
        # https://github.com/docker/setup-qemu-action
        name: Set up QEMU
        uses: docker/setup-qemu-action@v2
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2
      - name: Read VERSION file
        id: getversion
        run: echo "::set-output name=version::$(git describe --tags --dirty --always)"
      - name: Build
        uses: docker/build-push-action@v3
        with:
          file: ./dockerfiles/centos7.Dockerfile
          context: .
          # Currently github action runner only supports linux/amd64, it take a lot of time to build multi-arch image.
          # So we only build amd64 image for now.
          platforms: linux/amd64
          push: false
          tags: pingcap/tidb-dashboard:${{ steps.getversion.outputs.version }}
          no-cache: false
          pull: false
          cache-from: type=gha
          cache-to: type=gha,mode=max
