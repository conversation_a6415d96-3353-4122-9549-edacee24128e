# Copyright 2024 PingCAP, Inc. Licensed under Apache-2.0.
name: Upload E2E Snapshots

on:
  workflow_dispatch:
    inputs:
      ref:
        description: "The branch, tag or SHA to create snapshots"
        required: true
      spec:
        description: "Specify the spec files to run, example: `topsql/topsql.spec.ts`"
        required: true

jobs:
  e2e_test_snapshots:
    name: Take E2E Test Snapshots
    runs-on: ubuntu-latest
    timeout-minutes: 30
    strategy:
      fail-fast: false
      matrix:
        # test latest features and compatibility of lower version
        include:
          - feature_version: 6.0.0
            tidb_version: latest
          - feature_version: 5.4.0
            tidb_version: v5.4.0
          - feature_version: 5.0.0
            tidb_version: v5.0.0
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
        with:
          ref: ${{ github.event.inputs.ref }}
      # https://pnpm.io/continuous-integration#github-actions
      - name: Setup PNPM
        uses: pnpm/action-setup@v2
        with:
          version: 8
      - name: Setup Node.js
        uses: actions/setup-node@v2
        with:
          node-version: "18"
          cache: "pnpm"
          cache-dependency-path: "ui/pnpm-lock.yaml"
      - uses: actions/setup-go@v3
        with:
          go-version: "1.21"
      - name: Load go module cache
        uses: actions/cache@v3
        with:
          path: |
            ~/.cache/go-build
            ~/go/pkg/mod
          key: ${{ runner.os }}-go-${{ hashFiles('**/go.sum') }}
          restore-keys: |
            ${{ runner.os }}-go-
      - name: Load TiUP cache
        uses: actions/cache@v3
        with:
          path: ~/.tiup/components
          key: ${{ runner.os }}-tiup
          restore-keys: |
            ${{ runner.os }}-tiup
      - name: Install and run TiUP in the background
        run: |
          chmod u+x scripts/start_tiup.sh
          scripts/start_tiup.sh ${{ matrix.tidb_version }} false
      - name: Build UI
        run: |
          make ui
        env:
          NO_MINIMIZE: true
          CI: true
      - name: Wait TiUP Playground
        run: |
          chmod u+x scripts/wait_tiup_playground.sh
          scripts/wait_tiup_playground.sh 15 20
      - name: Debug TiUP
        run: |
          source /home/<USER>/.profile
          tiup --version
          ls /home/<USER>/.tiup/components/playground/
          DATA_PATH=$(ls /home/<USER>/.tiup/data/)
          echo $DATA_PATH
          echo "==== TiDB Log ===="
          head -n 3 /home/<USER>/.tiup/data/$DATA_PATH/tidb-0/tidb.log
          echo "==== TiKV Log ===="
          head -n 3 /home/<USER>/.tiup/data/$DATA_PATH/tikv-0/tikv.log
          echo "==== PD Log ===="
          head -n 3 /home/<USER>/.tiup/data/$DATA_PATH/pd-0/pd.log
      - name: Build and run backend in the background
        run: |
          make
          make run &
        env:
          UI: 1
          FEATURE_VERSION: ${{ matrix.feature_version }}
      - name: Delete Previous Snapshots
        run: rm -rf ${{ github.workspace }}/ui/packages/tidb-dashboard-for-op/cypress/snapshots
      - name: Run E2E Features Test
        run: make e2e_test_specify
        env:
          SERVER_URL: http://127.0.0.1:12333/dashboard/
          CI: true
          FEATURE_VERSION: ${{ matrix.feature_version }}
          TIDB_VERSION: ${{ matrix.tidb_version }}
          CYPRESS_ALLOW_SCREENSHOT: true
          E2E_SPEC: cypress/integration/${{ github.event.inputs.spec }}
      - name: Archive Test Video
        if: always()
        uses: actions/upload-artifact@v2
        with:
          name: e2e-video-${{ matrix.feature_version }}
          path: ${{ github.workspace }}/ui/packages/tidb-dashboard-for-op/cypress/videos/**/*
      - name: Upload snapshots artifact
        uses: actions/upload-artifact@v2
        if: always()
        with:
          name: e2e-snapshots-${{ matrix.feature_version }}
          path: ${{ github.workspace }}/ui/packages/tidb-dashboard-for-op/cypress/snapshots/**/*
