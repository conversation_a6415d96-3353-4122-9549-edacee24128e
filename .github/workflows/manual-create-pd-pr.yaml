name: Create PD PR Manually

on:
  workflow_dispatch:
    inputs:
      release_version:
        description: 'Release version, e.g. v7.6.0-f7bbcdcf'
        required: true
      pd_branchs:
        description: 'PD branch, e.g. ["master", "release-7.6"]'
        default: '["master"]'
        required: true

jobs:
  pd_pr:
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        # https://stackoverflow.com/questions/69781005/combine-dynamic-github-workflow-matrix-with-input-values-and-predefined-values
        branch: ${{ fromJson(github.event.inputs.pd_branchs) }}
    name: Create PD PR - ${{ matrix.branch }}
    steps:
      - name: Check out PD code base
        uses: actions/checkout@v3
        with:
          repository: tikv/pd
          ref: ${{ matrix.branch }}
      - uses: actions/setup-go@v3
        with:
          go-version: '1.21'
      - name: Load go module cache
        uses: actions/cache@v3
        with:
          path: |
            ~/.cache/go-build
            ~/go/pkg/mod
          key: ${{ runner.os }}-go-pd-${{ hashFiles('**/go.sum') }}
          restore-keys: |
            ${{ runner.os }}-go-pd-
      - name: Update TiDB Dashboard in PD code base
        run: |
          scripts/update-dashboard.sh ${{ github.event.inputs.release_version }}
      - name: Commit PD code base change
        id: git_commit
        run: |
          git diff
          git config user.name "baurine"
          git config user.email "<EMAIL>"
          git add .
          if git status | grep -q "Changes to be committed"
          then
            git commit --signoff --message "chore(dashboard): update TiDB Dashboard to ${{ github.event.inputs.release_version }}"
            echo "::set-output name=committed::1"
          else
            echo "No changes detected, skipped"
          fi
      - name: Set build ID
        id: build_id
        run: echo "::set-output name=id::$(date +%s)"
      - name: Create PR based on PD code base
        id: cpr
        uses: peter-evans/create-pull-request@v3
        if: steps.git_commit.outputs.committed == 1
        with:
          push-to-fork: baurine/pd
          token: ${{ secrets.PAT_TO_PUSH_PD_FORK }}
          branch: update-tidb-dashboard/${{ matrix.branch }}-${{ github.event.inputs.release_version }}-${{ steps.build_id.outputs.id }}
          title: 'chore(dashboard): update TiDB Dashboard to ${{ github.event.inputs.release_version }} [${{ matrix.branch }}]'
          body: |
            ### What problem does this PR solve?

            Issue Number: ref #4257

            Update TiDB Dashboard to [${{ github.event.inputs.release_version }}](https://github.com/pingcap/tidb-dashboard/releases/tag/${{ github.event.inputs.release_version }}).

            ### Release note

            ```release-note
            None
            ```
      - name: Check outputs
        if: steps.git_commit.outputs.committed == 1
        run: |
          echo "Pull Request Number - ${{ steps.cpr.outputs.pull-request-number }}"
          echo "Pull Request URL - ${{ steps.cpr.outputs.pull-request-url }}"
