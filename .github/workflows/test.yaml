name: Test

on:
  push:
    branches:
      - master
  pull_request:
    branches:
      - master
      - release

jobs:
  backend_ut:
    name: Backend - Unit
    runs-on: ubuntu-latest
    timeout-minutes: 10
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      - uses: actions/setup-go@v3
        with:
          go-version: "1.21"
      - name: Load go module cache
        uses: actions/cache@v3
        with:
          path: |
            ~/.cache/go-build
            ~/go/pkg/mod
          key: ${{ runner.os }}-go-${{ hashFiles('**/go.sum') }}
          restore-keys: |
            ${{ runner.os }}-go-
      - name: Run unit test
        run: |
          make unit_test
      # - name: Upload coverage to Codecov
      #   uses: codecov/codecov-action@v2
      #   with:
      #     files: ./coverage/unit_test.txt
      #     fail_ci_if_error: true
      #     flags: backend_ut
      #     verbose: true

  backend_integration:
    runs-on: ubuntu-latest
    timeout-minutes: 20
    strategy:
      matrix:
        # tidb_version: [nightly, ^6.0, ^5.4, ^5.0]
        tidb_version: [nightly, ^6.0]
    name: Backend - Integration - TiDB@${{ matrix.tidb_version }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      - uses: actions/setup-go@v3
        with:
          go-version: "1.21"
      - name: Load go module cache
        uses: actions/cache@v3
        with:
          path: |
            ~/.cache/go-build
            ~/go/pkg/mod
          key: ${{ runner.os }}-go-${{ hashFiles('**/go.sum') }}
          restore-keys: |
            ${{ runner.os }}-go-
      - name: Load TiUP cache
        uses: actions/cache@v3
        with:
          path: ~/.tiup/components
          key: ${{ runner.os }}-tiup-${{ matrix.tidb_version }}
      - name: Run integration test
        run: |
          make integration_test TIDB_VERSION=${{ matrix.tidb_version }}
      # - name: Upload coverage to Codecov
      #   uses: codecov/codecov-action@v2
      #   with:
      #     files: ./coverage/integration_${{ matrix.tidb_version }}.txt
      #     fail_ci_if_error: true
      #     flags: backend_integration
      #     verbose: true

  # e2e_test:
  #   runs-on: ubuntu-latest
  #   timeout-minutes: 30
  #   strategy:
  #     fail-fast: false
  #     matrix:
  #       # test latest features and compatibility of lower version
  #       include:
  #         - feature_version: 6.0.0 # You must ensure feature_version and tidb_version is matching!
  #           tidb_version: nightly
  #           without_ngm: true
  #         - feature_version: 6.0.0
  #           tidb_version: ^6.0
  #           without_ngm: true
  #         - feature_version: 5.4.0
  #           tidb_version: ^5.4
  #           without_ngm: true
  #         - feature_version: 5.0.0
  #           tidb_version: ^5.0
  #           without_ngm: true
  #   name: E2E - TiDB@${{ matrix.tidb_version }}${{ !matrix.without_ngm && '+ngm' || '' }}
  #   steps:
  #     - name: Checkout code
  #       uses: actions/checkout@v3
  #     # https://pnpm.io/continuous-integration#github-actions
  #     - name: Setup PNPM
  #       uses: pnpm/action-setup@v2.2.2
  #       with:
  #         version: 7
  #     - name: Setup Node.js
  #       uses: actions/setup-node@v2
  #       with:
  #         node-version: "16"
  #         cache: "pnpm"
  #         cache-dependency-path: "ui/pnpm-lock.yaml"
  #     - name: Load cypress cache
  #       uses: actions/cache@v3
  #       id: cypress-cache
  #       with:
  #         path: ~/.cache/Cypress
  #         key: ${{ runner.os }}-cypress-${{ hashFiles('**/pnpm-lock.yaml') }}
  #         restore-keys: |
  #           ${{ runner.os }}-cypress-
  #     - uses: actions/setup-go@v3
  #       with:
  #         go-version: "1.21"
  #     - name: Load go module cache
  #       uses: actions/cache@v3
  #       with:
  #         path: |
  #           ~/.cache/go-build
  #           ~/go/pkg/mod
  #         key: ${{ runner.os }}-go-${{ hashFiles('**/go.sum') }}
  #         restore-keys: |
  #           ${{ runner.os }}-go-
  #     - name: Load TiUP cache
  #       uses: actions/cache@v3
  #       with:
  #         path: ~/.tiup/components
  #         key: ${{ runner.os }}-tiup-${{ matrix.tidb_version }}
  #     - name: Install and run TiUP in the background
  #       run: |
  #         chmod u+x scripts/start_tiup.sh
  #         scripts/start_tiup.sh ${{ matrix.tidb_version }} ${{ matrix.without_ngm }}
  #     - name: Build UI
  #       run: |
  #         make ui
  #       env:
  #         NO_MINIMIZE: true
  #         CI: true
  #         E2E_TEST: true
  #     - name: Wait TiUP Playground
  #       run: |
  #         chmod u+x scripts/wait_tiup_playground.sh
  #         scripts/wait_tiup_playground.sh 15 20
  #     - name: Debug TiUP
  #       run: |
  #         source /home/<USER>/.profile
  #         tiup --version
  #         ls /home/<USER>/.tiup/components/playground/
  #         DATA_PATH=$(ls /home/<USER>/.tiup/data/)
  #         echo $DATA_PATH
  #         echo "==== TiDB Log ===="
  #         head -n 3 /home/<USER>/.tiup/data/$DATA_PATH/tidb-0/tidb.log
  #         echo "==== TiKV Log ===="
  #         head -n 3 /home/<USER>/.tiup/data/$DATA_PATH/tikv-0/tikv.log
  #         echo "==== PD Log ===="
  #         head -n 3 /home/<USER>/.tiup/data/$DATA_PATH/pd-0/pd.log
  #     - name: Build and run backend in the background
  #       run: |
  #         make
  #         make run &
  #       env:
  #         UI: 1
  #         FEATURE_VERSION: ${{ matrix.feature_version }}
  #     - name: Run E2E Features Test
  #       run: make e2e_test
  #       env:
  #         SERVER_URL: http://127.0.0.1:12333/dashboard/
  #         CI: true
  #         FEATURE_VERSION: ${{ matrix.feature_version }}
  #         TIDB_VERSION: ${{ matrix.tidb_version }}
  #         CYPRESS_ALLOW_SCREENSHOT: true
  #         WITHOUT_NGM: ${{ matrix.without_ngm }}
  #     - name: Upload coverage to Codecov
  #       uses: codecov/codecov-action@v2
  #       with:
  #         files: ./ui/packages/tidb-dashboard-for-op/.nyc_output/out.json
  #         fail_ci_if_error: false
  #         flags: e2e_test
  #         verbose: true
