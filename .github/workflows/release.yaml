name: Release

on:
  push:
    tags:
      - "v*"
      - "!v*-alpha"

jobs:
  release:
    name: Release
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      # https://pnpm.io/continuous-integration#github-actions
      - name: Setup PNPM
        uses: pnpm/action-setup@v2
        with:
          version: 8
      - name: Setup Node.js
        uses: actions/setup-node@v2
        with:
          node-version: "18"
          cache: "pnpm"
          cache-dependency-path: "ui/pnpm-lock.yaml"
      - uses: actions/setup-go@v3
        with:
          go-version: "1.21"
      - name: Load go module cache
        uses: actions/cache@v3
        with:
          path: |
            ~/.cache/go-build
            ~/go/pkg/mod
          key: ${{ runner.os }}-go-${{ hashFiles('**/go.sum') }}
          restore-keys: |
            ${{ runner.os }}-go-

      - name: Build UI
        env:
          REACT_APP_MIXPANEL_TOKEN: ${{ secrets.REACT_APP_MIXPANEL_TOKEN }}
        run: |
          make ui
      - name: Pack UI assets for release
        working-directory: ui/packages/tidb-dashboard-for-op/dist
        run: |
          zip -r ../static-assets.zip .

      # TODO: generate changelog
      # - name: Generate Changelog
      #   id: build_changelog
      #   uses: mikepenz/release-changelog-builder-action@v4.1.0
      - name: Create release
        id: create_release
        uses: fleskesvor/create-release@feature/support-target-commitish
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: ${{ github.ref }}
          release_name: Internal Version ${{ github.ref }}
          draft: false
          prerelease: false
          # body: ${{steps.build_changelog.outputs.changelog}}

      - name: Upload UI assets
        uses: actions/upload-release-asset@v1.0.1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          upload_url: ${{ steps.create_release.outputs.upload_url }}
          asset_path: ./ui/packages/tidb-dashboard-for-op/static-assets.zip
          asset_name: static-assets.zip
          asset_content_type: application/zip
      - name: Generate embedded UI assets
        run: |
          NO_ASSET_BUILD_TAG=1 scripts/embed_ui_assets.sh
          cp pkg/uiserver/embedded_assets_handler.go embedded_assets_handler.go
      - name: Pack embedded assets for release
        run: |
          zip -r embedded-assets-golang.zip ./embedded_assets_handler.go
      - name: Upload embedded UI assets
        uses: actions/upload-release-asset@v1.0.1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          upload_url: ${{ steps.create_release.outputs.upload_url }}
          asset_path: ./embedded-assets-golang.zip
          asset_name: embedded-assets-golang.zip
          asset_content_type: application/zip
